package com.jandar.dims.core.constants;

import com.jandar.dims.DimsApplication;

/**
 * <AUTHOR>
 */
public interface ProjectConstants {

    /**
     * 项目的版本号
     */
    String PROJECT_VERSION = "0.0.4";
    String PROJECT_DESCRIPTION = "DIMS(文书集成管理系统)";

    /**
     * 项目的更新时间
     */
    String PROJECT_UPDATE_DATETIME = "2025-06-25";

    /**
     * 项目的模块名称
     */
    String PROJECT_MODULE_NAME = "dims-backend";

    /**
     * 异常枚举的步进值
     */
    String BUSINESS_EXCEPTION_STEP_CODE = "100";

    /**
     * 项目的包名，例如com.jandar.dims
     */
    String ROOT_PACKAGE_NAME = DimsApplication.class.getPackage().getName();

}
