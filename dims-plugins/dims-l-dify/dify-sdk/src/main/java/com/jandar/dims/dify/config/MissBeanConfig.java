package com.jandar.dims.dify.config;

import com.jandar.dims.dify.service.IDifyConfigService;
import com.jandar.dims.dify.service.cond.CondDifyConfigServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class MissBeanConfig {
    private final DifyConfig difyConfig;

    public MissBeanConfig(DifyConfig difyConfig) {
        this.difyConfig = difyConfig;
    }

    @Bean
    @ConditionalOnMissingBean(IDifyConfigService.class)
    public IDifyConfigService iDifyConfigService() {
        return new CondDifyConfigServiceImpl(difyConfig);
    }
}
