package com.jandar.dims.dify.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.jandar.dims.dify.config.DifyConfig;
import com.jandar.dims.dify.entity.dify.api.DifyApiKey;
import com.jandar.dims.dify.entity.request.ChatMessageRequest;
import com.jandar.dims.dify.entity.request.DatasetRequest;
import com.jandar.dims.dify.entity.request.WorkflowRequest;
import com.jandar.dims.dify.entity.response.ChatMessageResponse;
import com.jandar.dims.dify.entity.response.DatasetResponse;
import com.jandar.dims.dify.entity.response.WorkflowResponse;
import com.jandar.dims.dify.enums.DifyTypeEnum;
import com.jandar.dims.dify.exception.DifyException;
import com.jandar.dims.dify.exception.enums.DifyExceptionEnum;
import com.jandar.dims.dify.service.IDifyConfigService;
import com.jandar.dims.dify.service.IDifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DifyServiceImpl implements IDifyService {

    private final DifyConfig difyConfig;
    private final IDifyConfigService iDifyConfigService;

    public DifyServiceImpl(DifyConfig difyConfig,
                           IDifyConfigService iDifyConfigService) {
        this.difyConfig = difyConfig;
        this.iDifyConfigService = iDifyConfigService;
    }

    @Override
    public DatasetResponse datasetCreateByText(String datasetId, DatasetRequest request, DifyApiKey difyApiKey) {
        difyApiKey.setType(DifyTypeEnum.DATASET.getCode());
        String secret = iDifyConfigService.gainApiSecret(difyApiKey);
        String url = StrUtil.format("{}/v1/datasets/{}/document/create_by_text", difyConfig.getApiHost(), datasetId);
        DatasetResponse result;
        try (HttpResponse response = HttpUtil.createPost(url)
                .header("Content-Type", "application/json")
                .bearerAuth(secret)
                .body(JSONUtil.toJsonStr(request))
                .execute()) {
            result = new DatasetResponse();
            String responseBody = response.body();
            if (response.isOk()) {
                result = JSONUtil.toBean(responseBody, DatasetResponse.class);
            } else {
                throw new DifyException(DifyExceptionEnum.DIFY_API_ERROR, responseBody);
            }
        } catch (Exception e) {
            throw new DifyException(DifyExceptionEnum.DIFY_API_ERROR, e.getMessage());
        }
        return result;
    }

    @Override
    public WorkflowResponse workflowRun(WorkflowRequest request, DifyApiKey difyApiKey) {
        difyApiKey.setType(DifyTypeEnum.WORKFLOW.getCode());
        String secret = iDifyConfigService.gainApiSecret(difyApiKey);
        String url = StrUtil.format("{}/v1/workflows/run", difyConfig.getApiHost());
        WorkflowResponse result = null;
        try (HttpResponse response = HttpUtil.createPost(url)
                .header("Content-Type", "application/json")
                .bearerAuth(secret)
                .body(JSONUtil.toJsonStr(request))
                .execute()) {
            result = new WorkflowResponse();
            String responseBody = response.body();
            if (response.isOk()) {
                if (StrUtil.isNotBlank(responseBody)) {
                    try {
                        result = JSONUtil.toBean(responseBody, WorkflowResponse.class);
                    } catch (Exception e) {
                        log.error("解析Dify API响应失败", e);
                    }
                }
            } else {
                throw new DifyException(DifyExceptionEnum.DIFY_API_ERROR, responseBody);
            }
        }
        return result;
    }

    @Override
    public ChatMessageResponse chatMessage(ChatMessageRequest request, DifyApiKey difyApiKey) {
        difyApiKey.setType(DifyTypeEnum.CHAT.getCode());
        String secret = iDifyConfigService.gainApiSecret(difyApiKey);
        String url = StrUtil.format("{}/v1/chat-messages", difyConfig.getApiHost());
        ChatMessageResponse result = null;
        try (HttpResponse response = HttpUtil.createPost(url)
                .header("Content-Type", "application/json")
                .bearerAuth(secret)
                .body(JSONUtil.toJsonStr(request))
                .execute()) {
            result = new ChatMessageResponse();
            String responseBody = response.body();
            if (response.isOk()) {
                if (StrUtil.isNotBlank(responseBody)) {
                    try {
                        result = JSONUtil.toBean(responseBody, ChatMessageResponse.class);
                    } catch (Exception e) {
                        log.error("解析Dify Chat API响应失败", e);
                    }
                }
            } else {
                throw new DifyException(DifyExceptionEnum.DIFY_API_ERROR, responseBody);
            }

        }
        return result;
    }
}
