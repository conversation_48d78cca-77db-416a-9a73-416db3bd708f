package com.jandar.dims.dify.config;

import com.jandar.dims.dify.entity.dify.api.DifyApiKey;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "dify")
@Data
public class DifyConfig {

    private String apiHost;

    private List<DifyApiKey> apiKeys;

}
