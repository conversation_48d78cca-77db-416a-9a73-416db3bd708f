package com.jandar.dims.dify.entity.request;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Workflow运行请求实体类
 * <AUTHOR>
 */
@Data
public class WorkflowRequest {
    
    /**
     * 输入参数
     */
    private Map<String, Object> inputs;
    
    /**
     * 响应模式，如：blocking, streaming
     */
    private String response_mode;
    
    /**
     * 用户标识
     */
    private String user;

    /**
     * 构建Workflow运行请求
     * @param content 输入内容
     * @param responseMode 响应模式（blocking/streaming）
     * @param user 用户标识
     * @return WorkflowRunRequest
     */
    public static WorkflowRequest buildContent(String content, String responseMode, String user) {
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("content", content);
        WorkflowRequest request = new WorkflowRequest();
        request.setInputs(inputs);
        request.setResponse_mode(responseMode);
        request.setUser(user);
        return request;
    }

    /**
     * 构建默认的阻塞模式Workflow运行请求
     * @param content 输入内容
     * @param user 用户标识
     * @return WorkflowRunRequest
     */
    public static WorkflowRequest buildBlockingContent(String content, String user) {
        return buildContent(content, "blocking", user);
    }

    /**
     * 构建默认的流式模式Workflow运行请求
     * @param content 输入内容
     * @param user 用户标识
     * @return WorkflowRunRequest
     */
    public static WorkflowRequest buildStreamingContent(String content, String user) {
        return buildContent(content, "streaming", user);
    }

    public static WorkflowRequest buildBlockingInputs(Map<String, Object> inputs, String user) {
        WorkflowRequest request = new WorkflowRequest();
        request.setInputs(inputs);
        request.setResponse_mode("blocking");
        request.setUser(user);
        return request;
    }


}
