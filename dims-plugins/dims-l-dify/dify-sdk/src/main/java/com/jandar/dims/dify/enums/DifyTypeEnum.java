package com.jandar.dims.dify.enums;

import cn.hutool.core.util.ObjectUtil;
import com.jandar.dims.core.base.ReadableEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum DifyTypeEnum implements ReadableEnum<DifyTypeEnum> {

    DATASET("DATASET", "知识库"),
    WORKFLOW("WORKFLOW", "工作流"),
    CHAT("CHAT", "对话"),

    ;

    private final String code;

    private final String message;


    DifyTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Object getKey() {
        return this.code;
    }

    @Override
    public Object getName() {
        return this.message;
    }

    @Override
    public DifyTypeEnum parseToEnum(String originValue) {
        return staticParseToEnum(originValue);
    }

    public static DifyTypeEnum staticParseToEnum(String originValue) {
        if (ObjectUtil.isEmpty(originValue)) {
            return null;
        }
        for (DifyTypeEnum value : DifyTypeEnum.values()) {
            if (value.code.equalsIgnoreCase(originValue)) {
                return value;
            }
        }
        return null;
    }

}
