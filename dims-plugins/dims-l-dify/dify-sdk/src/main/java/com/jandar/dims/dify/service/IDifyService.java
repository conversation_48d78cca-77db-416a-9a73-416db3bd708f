package com.jandar.dims.dify.service;

import com.jandar.dims.dify.entity.dify.api.DifyApiKey;
import com.jandar.dims.dify.entity.request.ChatMessageRequest;
import com.jandar.dims.dify.entity.request.DatasetRequest;
import com.jandar.dims.dify.entity.request.WorkflowRequest;
import com.jandar.dims.dify.entity.response.ChatMessageResponse;
import com.jandar.dims.dify.entity.response.DatasetResponse;
import com.jandar.dims.dify.entity.response.WorkflowResponse;

/**
 * <AUTHOR>
 */
public interface IDifyService {

    /**
     * 通过文本创建数据集文档
     * @param datasetId 数据集ID
     * @param request 创建文档请求
     * @return 创建文档响应
     */
    DatasetResponse datasetCreateByText(String datasetId, DatasetRequest request, DifyApiKey difyApiKey);

    /**
     * 运行工作流
     * @param request 工作流运行请求
     * @return 工作流运行响应
     */
    WorkflowResponse workflowRun(WorkflowRequest request, DifyApiKey difyApiKey);

    /**
     * 发送聊天消息
     * @param request 聊天消息请求
     * @return 聊天消息响应
     */
    ChatMessageResponse chatMessage(ChatMessageRequest request, DifyApiKey difyApiKey);


}
