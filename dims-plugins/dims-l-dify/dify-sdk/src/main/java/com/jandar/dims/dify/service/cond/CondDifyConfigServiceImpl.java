package com.jandar.dims.dify.service.cond;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jandar.dims.dify.config.DifyConfig;
import com.jandar.dims.dify.entity.dify.api.DifyApiKey;
import com.jandar.dims.dify.exception.DifyException;
import com.jandar.dims.dify.exception.enums.DifyExceptionEnum;
import com.jandar.dims.dify.service.IDifyConfigService;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CondDifyConfigServiceImpl implements IDifyConfigService {

    private final DifyConfig difyConfig;

    public CondDifyConfigServiceImpl(DifyConfig difyConfig) {
        this.difyConfig = difyConfig;
    }

    private DifyApiKey gainApiKey(String type, String name) {
        List<DifyApiKey> apiKeys = difyConfig.getApiKeys();
        if (CollUtil.isEmpty(apiKeys)) {
            throw new DifyException(DifyExceptionEnum.DIFY_SECRET_ERROR);
        }
        return apiKeys.stream()
                .filter(apiKey -> apiKey.getType().equalsIgnoreCase(type) && apiKey.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String gainApiSecret(DifyApiKey difyApiKey) {
        String type = difyApiKey.getType();
        String name = difyApiKey.getName();
        String secret = difyApiKey.getSecret();
        if (StrUtil.isNotBlank(secret)) {
            return secret;
        }
        if (StrUtil.isBlank(name)) {
            name = "default";
        }
        if (StrUtil.isBlank(type)) {
            throw new DifyException(DifyExceptionEnum.DIFY_SECRET_ERROR);
        }
        DifyApiKey gainApiKey = this.gainApiKey(type, name);
        String gainSecret = gainApiKey.getSecret();
        if (StrUtil.isBlank(gainSecret)) {
            throw new DifyException(DifyExceptionEnum.DIFY_SECRET_ERROR);
        }
        return gainSecret;
    }

}
