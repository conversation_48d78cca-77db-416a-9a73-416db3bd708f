CREATE TABLE sys_dify_config (
     id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
     api_host VARCHAR(180) DEFAULT '' COMMENT 'API主机地址',
     name VARCHAR(180) DEFAULT '' COMMENT 'API密钥名称',
     type VARCHAR(180) DEFAULT '' COMMENT 'API密钥类型',
     secret VARCHAR(180) DEFAULT '' COMMENT 'API密钥',
     create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB COMMENT = 'Dify配置表';

