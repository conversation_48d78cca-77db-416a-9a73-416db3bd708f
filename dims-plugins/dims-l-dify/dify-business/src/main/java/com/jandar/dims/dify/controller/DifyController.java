package com.jandar.dims.dify.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.jandar.dims.core.entity.response.ResponseResult;
import com.jandar.dims.core.entity.response.SuccessResponseResult;
import com.jandar.dims.core.util.HttpServletUtil;
import com.jandar.dims.dify.entity.dify.api.DifyApiKey;
import com.jandar.dims.dify.entity.request.ChatMessageRequest;
import com.jandar.dims.dify.entity.request.DatasetRequest;
import com.jandar.dims.dify.entity.request.WorkflowRequest;
import com.jandar.dims.dify.entity.response.ChatMessageResponse;
import com.jandar.dims.dify.entity.response.DatasetResponse;
import com.jandar.dims.dify.entity.response.WorkflowResponse;
import com.jandar.dims.dify.service.IDifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * Dify AI服务控制器
 * <AUTHOR>
 */
@Tag(name = "Dify AI服务接口")
@ApiSort(3001)
@RestController
@RequestMapping("/dify")
public class DifyController {

    private final IDifyService iDifyService;

    public DifyController(IDifyService iDifyService) {
        this.iDifyService = iDifyService;
    }

    /**
     * 通过文本创建数据集文档
     */
    @PostMapping("/datasets/document/create/{dataset_id}")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "创建数据集文档", description = "通过文本内容创建数据集文档")
    public ResponseResult<?> createDatasetDocument(@PathVariable("dataset_id") String datasetId,
                                                   @RequestBody DatasetRequest request) {
        DifyApiKey difyApiKey = DifyApiKey.build(HttpServletUtil.getHeaderAuthorizationBearer());
        DatasetResponse datasetResponse = iDifyService.datasetCreateByText(datasetId, request, difyApiKey);
        return new SuccessResponseResult<>(datasetResponse);
    }

    /**
     * 运行工作流
     */
    @PostMapping("/workflows/run")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "运行工作流", description = "运行Dify工作流")
    public ResponseResult<?> runWorkflow(@RequestBody WorkflowRequest request) {
        DifyApiKey difyApiKey = DifyApiKey.build(HttpServletUtil.getHeaderAuthorizationBearer());
        WorkflowResponse workflowResponse = iDifyService.workflowRun(request, difyApiKey);
        return new SuccessResponseResult<>(workflowResponse);
    }

    /**
     * 发送聊天消息
     */
    @PostMapping("/chat-messages")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "发送聊天消息", description = "发送聊天消息到Dify AI")
    public ResponseResult<?> sendChatMessage(@RequestBody ChatMessageRequest request) {
        DifyApiKey difyApiKey = DifyApiKey.build(HttpServletUtil.getHeaderAuthorizationBearer());
        ChatMessageResponse chatMessageResponse = iDifyService.chatMessage(request, difyApiKey);
        return new SuccessResponseResult<>(chatMessageResponse);
    }

}