/*
package com.jandar.dims.dify.service.impl;

import com.jandar.dims.dify.config.DifyConfig;
import com.jandar.dims.dify.entity.dify.api.DifyApiKey;
import com.jandar.dims.dify.mybatis.service.ISysDifyConfigService;
import com.jandar.dims.dify.service.IDifyConfigService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

*/
/**
 * <AUTHOR>
 *//*

@ConditionalOnProperty(prefix = "dify", name = "database-enabled", havingValue = "true")
@Service
public class DifyConfigServiceImpl implements IDifyConfigService {

    private final DifyConfig difyConfig;
    private final ISysDifyConfigService iSysDifyConfigService;

    public DifyConfigServiceImpl(DifyConfig difyConfig,
                                 ISysDifyConfigService iSysDifyConfigService) {
        this.difyConfig = difyConfig;
        this.iSysDifyConfigService = iSysDifyConfigService;
    }

    @Override
    public String gainApiSecret(DifyApiKey difyApiKey) {
        String apiHost = difyConfig.getApiHost();

        return "";
    }
}
*/
