package com.jandar.dims.dify.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.jandar.dims.dify.entity.dify.api.DifyApiKey;
import com.jandar.dims.dify.entity.dify.workflow.WorkflowData;
import com.jandar.dims.dify.entity.dify.workflow.WorkflowDataOutputs;
import com.jandar.dims.dify.entity.request.DifyRequest;
import com.jandar.dims.dify.entity.request.WorkflowRequest;
import com.jandar.dims.dify.entity.response.WorkflowResponse;
import com.jandar.dims.dify.enums.DifyTypeEnum;
import com.jandar.dims.dify.exception.DifyException;
import com.jandar.dims.dify.exception.enums.DifyExceptionEnum;
import com.jandar.dims.dify.service.IDifyConfigService;
import com.jandar.dims.dify.service.IDifyService;
import com.jandar.dims.dify.service.IPunishItemService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class PunishItemServiceImpl implements IPunishItemService {

    private final IDifyService iDifyService;

    public PunishItemServiceImpl(IDifyService iDifyService) {
        this.iDifyService = iDifyService;
    }

    @Override
    public String gainWorkflowPunishItem(DifyRequest difyRequest) {
        String user = difyRequest.getUser();
        Map<String, Object> inputs = difyRequest.getInputs();
        if (StrUtil.isBlank(user) || MapUtil.isEmpty(inputs)) {
            throw new DifyException(DifyExceptionEnum.DIFY_API_ERROR, "用户标识或输入参数不能为空");
        }
        WorkflowRequest workflowRequest = WorkflowRequest.buildBlockingInputs(inputs, user);
        String name = difyRequest.getName();
        DifyApiKey difyApiKey = new DifyApiKey();
        difyApiKey.setType(DifyTypeEnum.WORKFLOW.getCode());
        difyApiKey.setName(name);
        WorkflowResponse workflowResponse = iDifyService.workflowRun(workflowRequest, difyApiKey);
        return Optional.ofNullable(workflowResponse)
                .map(WorkflowResponse::getData)
                .map(WorkflowData::getOutputs)
                .map(WorkflowDataOutputs::getText)
                .orElse(null);
    }
}
