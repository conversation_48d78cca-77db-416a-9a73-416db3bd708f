/*
package com.jandar.dims.dify.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

*/
/**
 * <p>
 * Dify配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 *//*

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_dify_config")
public class SysDifyConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    */
/**
     * 主键ID
     *//*

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    */
/**
     * API主机地址
     *//*

    @TableField("api_host")
    private String apiHost;

    */
/**
     * API密钥名称
     *//*

    @TableField("name")
    private String name;

    */
/**
     * API密钥类型
     *//*

    @TableField("type")
    private String type;

    */
/**
     * API密钥
     *//*

    @TableField("secret")
    private String secret;

    */
/**
     * 创建时间
     *//*

    @TableField("create_time")
    private LocalDateTime createTime;

    */
/**
     * 更新时间
     *//*

    @TableField("update_time")
    private LocalDateTime updateTime;

}
*/
