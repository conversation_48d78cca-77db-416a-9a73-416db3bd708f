/*
package com.jandar.dims.dify.mybatis.service.impl;

import com.jandar.dims.dify.mybatis.entity.SysDifyConfig;
import com.jandar.dims.dify.mybatis.mapper.SysDifyConfigMapper;
import com.jandar.dims.dify.mybatis.service.ISysDifyConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

*/
/**
 * <p>
 * Dify配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 *//*

@Slf4j
@Service
public class SysDifyConfigServiceImpl extends ServiceImpl<SysDifyConfigMapper, SysDifyConfig> implements ISysDifyConfigService {

}
*/
