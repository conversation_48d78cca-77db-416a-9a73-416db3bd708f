<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jandar.dims</groupId>
        <artifactId>dims-l-dify</artifactId>
        <version>0.0.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>dify-business</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jandar.dims</groupId>
            <artifactId>dify-sdk</artifactId>
            <version>0.0.1</version>
        </dependency>
        <!-- <dependency>
            <groupId>com.jandar.dims</groupId>
            <artifactId>database-spring-boot-starter</artifactId>
            <version>${dims.version}</version>
        </dependency> -->
    </dependencies>

</project>