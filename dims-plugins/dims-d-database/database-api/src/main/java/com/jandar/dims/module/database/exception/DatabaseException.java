package com.jandar.dims.module.database.exception;

import cn.hutool.core.util.StrUtil;
import com.jandar.dims.core.exception.AbstractExceptionEnum;
import com.jandar.dims.core.exception.base.ServiceException;
import com.jandar.dims.module.database.constants.DatabaseConstants;

/**
 * <AUTHOR>
 */
public class DatabaseException extends ServiceException {

    private static final long serialVersionUID = 9139142335305031242L;

    public DatabaseException(AbstractExceptionEnum exception, Object... params) {
        super(DatabaseConstants.DATABASE_MODULE_NAME, exception.getErrorCode(), StrUtil.format(exception.getUserTip(), params));
    }

    public DatabaseException(AbstractExceptionEnum exception) {
        super(DatabaseConstants.DATABASE_EXCEPTION_STEP_CODE, exception);
    }

}